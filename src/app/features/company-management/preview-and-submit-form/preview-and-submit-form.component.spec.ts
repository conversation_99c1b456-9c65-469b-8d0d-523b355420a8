import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { PreviewAndSubmitFormComponent } from './preview-and-submit-form.component';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';

describe('PreviewAndSubmitFormComponent', () => {
  let component: PreviewAndSubmitFormComponent;
  let fixture: ComponentFixture<PreviewAndSubmitFormComponent>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockSharedLookupService: jasmine.SpyObj<SharedLookupService>;

  beforeEach(async () => {
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState'
    ]);
    mockSharedLookupService = jasmine.createSpyObj('SharedLookupService', [
      'isValidEmail'
    ]);

    // Mock form groups
    const mockForm = jasmine.createSpyObj('FormGroup', ['get']);
    const mockControl = jasmine.createSpyObj('FormControl', ['value']);
    mockControl.value = 'test value';
    mockForm.get.and.returnValue(mockControl);

    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: {
        name: 'Test Company',
        abn: '*********',
        acn: '*********',
        billingEmail: '<EMAIL>',
        addressLine1: '123 Test St',
        state: 1,
        postalCode: 1
      },
      billingDetails: {
        name: 'Test Billing',
        addressLine1: '456 Billing St',
        state: 2,
        postalCode: 2,
        sameAsCompanyDetails: false
      },
      userDetails: [
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          contactNumber: '**********',
          roleId: 1
        }
      ]
    });

    mockSharedLookupService.isValidEmail.and.returnValue(true);

    await TestBed.configureTestingModule({
      imports: [
        PreviewAndSubmitFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: SharedLookupService, useValue: mockSharedLookupService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PreviewAndSubmitFormComponent);
    component = fixture.componentInstance;

    // Set up mock forms
    component.companyForm = mockForm;
    component.billingForm = mockForm;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with onboarding state data', () => {
    component.ngOnInit();

    expect(mockCompanyStateService.getCurrentOnboardingState).toHaveBeenCalled();
    expect(component.companyDetails).toBeDefined();
    expect(component.billingDetails).toBeDefined();
    expect(component.userDetails).toBeDefined();
  });

  it('should get company form value', () => {
    const value = component.getCompanyFormValue('name');
    expect(value).toBe('test value');
  });

  it('should get billing form value', () => {
    const value = component.getBillingFormValue('name');
    expect(value).toBe('test value');
  });

  it('should get state name by ID', () => {
    component.states = [
      { id: 1, stateName: 'NSW' },
      { id: 2, stateName: 'VIC' }
    ] as any[];

    expect(component.getStateName(1)).toBe('NSW');
    expect(component.getStateName(2)).toBe('VIC');
    expect(component.getStateName(999)).toBe('N/A');
  });

  it('should get zipcode by ID', () => {
    component.zipcodes = [
      { id: 1, zipCode: '2000' },
      { id: 2, zipCode: '3000' }
    ] as any[];

    expect(component.getZipcode(1)).toBe('2000');
    expect(component.getZipcode(2)).toBe('3000');
    expect(component.getZipcode(999)).toBe('N/A');
  });

  it('should get role name by ID', () => {
    component.roles = [
      { id: 1, displayText: 'Admin' },
      { id: 2, displayText: 'User' }
    ] as any[];

    expect(component.getRoleName(1)).toBe('Admin');
    expect(component.getRoleName(2)).toBe('User');
    expect(component.getRoleName(999)).toBe('N/A');
  });

  it('should validate email using SharedLookupService', () => {
    const email = '<EMAIL>';

    const result = component.isValidEmail(email);

    expect(mockSharedLookupService.isValidEmail).toHaveBeenCalledWith(email);
    expect(result).toBeTrue();
  });

  it('should emit submit event', () => {
    spyOn(component.submitForm, 'emit');

    component.onSubmit();

    expect(component.submitForm.emit).toHaveBeenCalled();
  });

  it('should emit previous step event', () => {
    spyOn(component.previousStep, 'emit');

    component.onPreviousStep();

    expect(component.previousStep.emit).toHaveBeenCalled();
  });

  it('should handle missing company details', () => {
    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null
    });

    component.ngOnInit();

    expect(component.companyDetails).toBeNull();
    expect(component.billingDetails).toBeNull();
    expect(component.userDetails).toBeNull();
  });

  it('should handle same as company details billing', () => {
    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: {
        name: 'Test Company',
        addressLine1: '123 Test St'
      },
      billingDetails: {
        sameAsCompanyDetails: true
      },
      userDetails: []
    });

    component.ngOnInit();

    expect(component.billingDetails.sameAsCompanyDetails).toBeTrue();
  });

  it('should handle empty user details array', () => {
    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: {},
      billingDetails: {},
      userDetails: []
    });

    component.ngOnInit();

    expect(component.userDetails).toEqual([]);
  });
});
