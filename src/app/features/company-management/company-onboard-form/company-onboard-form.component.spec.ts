import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyOnboardFormComponent } from './company-onboard-form.component';
import { NotificationService } from '../../../core/services/notification.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { CompanyFormService } from '../../../core/services/company-form.service';
import { CompanyValidationService } from '../../../core/services/company-validation.service';

describe('CompanyOnboardFormComponent', () => {
  let component: CompanyOnboardFormComponent;
  let fixture: ComponentFixture<CompanyOnboardFormComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockCompanyFormService: jasmine.SpyObj<CompanyFormService>;
  let mockCompanyValidationService: jasmine.SpyObj<CompanyValidationService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState', 'updateCompanyDetails', 'updateBillingDetails'
    ]);
    mockCompanyFormService = jasmine.createSpyObj('CompanyFormService', [
      'createCompanyForm', 'createBillingForm', 'getDefaultBillingDetailsObject',
      'clearBillingForm', 'fetchStates', 'fetchZipcodes'
    ]);
    mockCompanyValidationService = jasmine.createSpyObj('CompanyValidationService', [
      'updateBillingFormValidators'
    ]);

    // Mock form creation
    const mockForm = jasmine.createSpyObj('FormGroup', ['patchValue', 'get', 'valueChanges']);
    mockForm.get.and.returnValue(jasmine.createSpyObj('FormControl', ['setValue', 'valueChanges']));
    mockForm.valueChanges = of({});

    mockCompanyFormService.createCompanyForm.and.returnValue(mockForm);
    mockCompanyFormService.createBillingForm.and.returnValue(mockForm);
    mockCompanyFormService.fetchStates.and.returnValue(of([]));
    mockCompanyFormService.fetchZipcodes.and.returnValue(of([]));
    mockCompanyFormService.getDefaultBillingDetailsObject.and.returnValue({});

    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null,
      currentStep: 0
    });

    await TestBed.configureTestingModule({
      imports: [
        CompanyOnboardFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: CompanyFormService, useValue: mockCompanyFormService },
        { provide: CompanyValidationService, useValue: mockCompanyValidationService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyOnboardFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.sameAsCompanyDetails).toBeFalse();
    expect(component.isBillingStep).toBeFalse();
    expect(component.isCompanyStep).toBeTrue();
  });

  it('should create forms on initialization', () => {
    expect(mockCompanyFormService.createCompanyForm).toHaveBeenCalled();
    expect(mockCompanyFormService.createBillingForm).toHaveBeenCalled();
  });

  it('should fetch states on init', () => {
    component.ngOnInit();
    expect(mockCompanyFormService.fetchStates).toHaveBeenCalled();
  });

  it('should handle same as company details change', () => {
    component.sameAsCompanyDetails = false;

    component.onSameAsCompanyDetailsChange();

    expect(component.onSameAsCompanyDetailsChange).toBeDefined();
  });

  it('should fetch zipcodes when state changes', () => {
    const stateId = 1;

    component.fetchZipcodes(stateId);

    expect(mockCompanyFormService.fetchZipcodes).toHaveBeenCalledWith(stateId);
  });

  it('should update billing form validators', () => {
    component.sameAsCompanyDetails = true;

    // Call private method through public interface
    component.ngOnInit();

    expect(mockCompanyValidationService.updateBillingFormValidators).toHaveBeenCalled();
  });

  it('should clear billing form when same as company details is false', () => {
    component.sameAsCompanyDetails = false;

    // Simulate the private method call
    component.onSameAsCompanyDetailsChange();

    expect(mockCompanyFormService.clearBillingForm).toHaveBeenCalled();
  });

  it('should get current form based on step', () => {
    component.isBillingStep = false;
    expect(component.currentForm).toBe(component.companyForm);

    component.isBillingStep = true;
    expect(component.currentForm).toBe(component.billingForm);
  });

  it('should handle loading state', () => {
    component.isLoading = true;
    expect(component.isLoading).toBeTrue();

    component.isLoading = false;
    expect(component.isLoading).toBeFalse();
  });

  it('should emit same as company details change', () => {
    spyOn(component.sameAsCompanyDetailsChange, 'emit');

    component.onSameAsCompanyDetailsChange();

    expect(component.sameAsCompanyDetailsChange.emit).toHaveBeenCalledWith(component.sameAsCompanyDetails);
  });
});
