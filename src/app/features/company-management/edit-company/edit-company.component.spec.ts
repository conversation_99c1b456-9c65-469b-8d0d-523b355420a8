import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { EditCompanyComponent } from './edit-company.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyStateService } from '../../../core/services/company-state.service';

describe('EditCompanyComponent', () => {
  let component: EditCompanyComponent;
  let fixture: ComponentFixture<EditCompanyComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['getCompanyById']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'setCompanyData', 'getCompanyData', 'clearTempCompanyData'
    ]);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: { params: { id: '1' } },
      params: of({ id: '1' }),
      queryParams: of({})
    };

    mockRegisterService.getCompanyById.and.returnValue(of({
      success: true,
      data: {
        id: 1,
        name: 'Test Company',
        abn: '*********',
        acn: '*********'
      }
    }));

    await TestBed.configureTestingModule({
      imports: [
        EditCompanyComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EditCompanyComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.company).toBeNull();
    expect(component.companyId).toBeNull();
  });

  it('should fetch company data on init', () => {
    component.ngOnInit();

    expect(mockRegisterService.getCompanyById).toHaveBeenCalledWith(1);
  });

  it('should set company data when fetched successfully', () => {
    component.ngOnInit();

    expect(component.company).toBeDefined();
    expect(component.companyId).toBe(1);
    expect(mockCompanyStateService.setCompanyData).toHaveBeenCalled();
  });

  it('should handle error when fetching company data', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: false, message: 'Error' }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should navigate back to company grid on form cancel', () => {
    component.onFormCancelled();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/company-management']);
  });

  it('should navigate back to company grid on company save', () => {
    const mockCompany = { id: 1, name: 'Test Company' } as any;

    component.onCompanySaved(mockCompany);

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/company-management']);
    expect(mockNotificationService.success).toHaveBeenCalled();
  });

  it('should handle loading state during company fetch', () => {
    expect(component.isLoading).toBeFalse();

    component.ngOnInit();

    // Loading should be set to true during fetch, then false after
    expect(component.isLoading).toBeFalse();
  });

  it('should extract company ID from route params', () => {
    component.ngOnInit();

    expect(component.companyId).toBe(1);
  });

  it('should handle invalid company ID', () => {
    mockActivatedRoute.snapshot.params.id = 'invalid';

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should clear temp company data on destroy', () => {
    component.ngOnDestroy();

    expect(mockCompanyStateService.clearTempCompanyData).toHaveBeenCalled();
  });

  it('should show error when company not found', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: true, data: null }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should handle network error during company fetch', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: false, message: 'Network error' }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalledWith('Network error');
  });
});
