<nz-breadcrumb>
  <nz-breadcrumb-item>
    <span class="non-active-breadcrumb" (click)="goBack()"
      >Company Management</span
    >
  </nz-breadcrumb-item>
  <nz-breadcrumb-item>
    <span class="active-breadcrumb">View {{ company?.name }}</span>
  </nz-breadcrumb-item>
</nz-breadcrumb>

<div class="company-view-tabs">
  <nz-tabset class="tabset" [nzTabBarGutter]="32">
    <!-- Company Details -->
    <nz-tab nzTitle="Company Details">
      <ng-template nz-tab>
        <div class="row company-details-container">
          @if (!isEditFormVisible) {
            <div class="col-md-2">
              <p class="field-label">Company Name:</p>
              <p class="field-label">ABN:</p>
              <p class="field-label">ACN:</p>
              <p class="field-label">Contact:</p>
              <p class="field-label">Email Id:</p>
              <p class="field-label">Address:</p>
              <p class="field-label">Billing Address:</p>
            </div>
            <div class="col-md-8">
              <p>{{ company?.name || 'N/A' }}</p>
              <p>{{ company?.abn || 'N/A' }}</p>
              <p>{{ company?.acn || 'N/A' }}</p>
              <p>{{ company?.accountsContactName || 'N/A' }}</p>
              <p>{{ company?.billingEmail || 'N/A' }}</p>
              <p>{{ primaryAddressFormatted || 'N/A' }}</p>
              <p>{{ billingAddressFormatted || 'N/A' }}</p>
            </div>
            <div class="col-md-2">
              <div
                class="action-buttons d-flex align-items-end"
                style="margin-bottom: 16px"
              >
                <nz-icon
                  nzType="edit"
                  nzTheme="outline"
                  class="nz-icon-style"
                  (click)="editCompany()"
                />
                <nz-switch
                  [ngModel]="company?.isActive"
                  [nzCheckedChildren]="'Active'"
                  [nzUnCheckedChildren]="'Inactive'"
                  (click)="toggleActive()"
                ></nz-switch>
              </div>
            </div>
          } @else {
            <!-- Edit Company Form Component -->
            <app-edit-company-form
              [isVisible]="isEditFormVisible"
              [company]="company"
              (companySaved)="onCompanySaved($event)"
              (formCancelled)="onFormCancelled()"
            >
            </app-edit-company-form>
          }
          <!-- Active/Inactive Users Component -->
          <app-active-inactive-users
            [companyId]="companyId || null"
            [company]="company"
          >
          </app-active-inactive-users>
        </div>
      </ng-template>
    </nz-tab>

    <!-- Billing Details -->
    <nz-tab nzTitle="Billing Details">
      <ng-template nz-tab>
        <p>Billing details content goes here...</p>
      </ng-template>
    </nz-tab>

    <!-- Pricing Management -->
    <nz-tab nzTitle="Pricing Management">
      <ng-template nz-tab>
        <app-data-grid
          [tableColumns]="pricingManagementCompanyLevelTableColumns"
          [tableData]="pricingManagementTableData"
          [loading]="isLoading"
          [showPagination]="true"
          (tableDataClick)="onPricingManagementTableDataClick($event)"
        >
        </app-data-grid>

        <nz-drawer
          [nzClosable]="false"
          [nzVisible]="openPricingView"
          [nzTitle]="drawerTitle"
          [nzExtra]="extra"
          (nzOnClose)="onClosePricingDetails()"
          data-testid="pricing-details-drawer"
        >
          <ng-container *nzDrawerContent>
            <app-data-grid
              [tableColumns]="viewPricingTableColumns"
              [tableData]="viewPricingTableData"
              [loading]="isLoading"
              [showPagination]="false"
              data-testid="view-pricing-table"
            >
            </app-data-grid>
          </ng-container>
        </nz-drawer>
        <ng-template #drawerTitle>
          <h2>View Pricing</h2>
        </ng-template>

        <ng-template #extra>
          <button
            nz-button
            nzType="primary"
            nzGhost
            (click)="onClosePricingDetails()"
            data-testid="property-details-drawer-close-btn"
          >
            <nz-icon nzType="close-circle" nzTheme="fill" />
            Close
          </button>
        </ng-template>
      </ng-template>
    </nz-tab>
  </nz-tabset>
</div>
