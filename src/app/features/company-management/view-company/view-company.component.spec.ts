import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { ViewCompanyComponent } from './view-company.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyStateService } from '../../../core/services/company-state.service';

describe('ViewCompanyComponent', () => {
  let component: ViewCompanyComponent;
  let fixture: ComponentFixture<ViewCompanyComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['getCompanyById', 'getAllUsers']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'setCompanyData', 'getCompanyData'
    ]);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: { params: { id: '1' } },
      params: of({ id: '1' }),
      queryParams: of({})
    };

    mockRegisterService.getCompanyById.and.returnValue(of({
      success: true,
      data: {
        id: 1,
        name: 'Test Company',
        abn: '*********',
        acn: '*********',
        billingEmail: '<EMAIL>',
        primaryAddress: {
          addressLine1: '123 Test St',
          stateName: 'NSW',
          zipCode: '2000'
        },
        billingAddress: {
          addressLine1: '456 Billing St',
          stateName: 'VIC',
          zipCode: '3000'
        }
      }
    }));

    mockRegisterService.getAllUsers.and.returnValue(of({
      success: true,
      data: {
        content: [
          {
            id: 1,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            active: true
          }
        ]
      }
    }));

    await TestBed.configureTestingModule({
      imports: [
        ViewCompanyComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ViewCompanyComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.company).toBeNull();
    expect(component.companyId).toBeNull();
    expect(component.isCompanyDetailsAccordionOpen).toBeTrue();
    expect(component.isBillingDetailsAccordionOpen).toBeFalse();
    expect(component.isUsersAccordionOpen).toBeFalse();
  });

  it('should fetch company data on init', () => {
    component.ngOnInit();

    expect(mockRegisterService.getCompanyById).toHaveBeenCalledWith(1);
    expect(mockRegisterService.getAllUsers).toHaveBeenCalledWith(1);
  });

  it('should set company data when fetched successfully', () => {
    component.ngOnInit();

    expect(component.company).toBeDefined();
    expect(component.companyId).toBe(1);
    expect(mockCompanyStateService.setCompanyData).toHaveBeenCalled();
  });

  it('should handle error when fetching company data', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: false, message: 'Error' }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should toggle company details accordion', () => {
    const initialState = component.isCompanyDetailsAccordionOpen;

    component.toggleCompanyDetailsAccordion();

    expect(component.isCompanyDetailsAccordionOpen).toBe(!initialState);
  });

  it('should toggle billing details accordion', () => {
    const initialState = component.isBillingDetailsAccordionOpen;

    component.toggleBillingDetailsAccordion();

    expect(component.isBillingDetailsAccordionOpen).toBe(!initialState);
  });

  it('should toggle users accordion', () => {
    const initialState = component.isUsersAccordionOpen;

    component.toggleUsersAccordion();

    expect(component.isUsersAccordionOpen).toBe(!initialState);
  });

  it('should navigate to edit company', () => {
    component.companyId = 1;

    component.editCompany();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/company-management/edit', 1]);
  });

  it('should navigate back to company grid', () => {
    component.goBack();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/company-management']);
  });

  it('should handle user updated event', () => {
    spyOn(component, 'getUsers');

    component.onUserUpdated();

    expect(component.getUsers).toHaveBeenCalled();
  });

  it('should extract company ID from route params', () => {
    component.ngOnInit();

    expect(component.companyId).toBe(1);
  });

  it('should handle invalid company ID', () => {
    mockActivatedRoute.snapshot.params.id = 'invalid';

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should show error when company not found', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: true, data: null }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should handle network error during company fetch', () => {
    mockRegisterService.getCompanyById.and.returnValue(of({ success: false, message: 'Network error' }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalledWith('Network error');
  });

  it('should handle users fetch error', () => {
    mockRegisterService.getAllUsers.and.returnValue(of({ success: false, message: 'Users error' }));

    component.ngOnInit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should display company address correctly', () => {
    component.company = {
      primaryAddress: {
        addressLine1: '123 Test St',
        stateName: 'NSW',
        zipCode: '2000'
      }
    } as any;

    // Test that the component can access address properties
    expect(component.company.primaryAddress.addressLine1).toBe('123 Test St');
    expect(component.company.primaryAddress.stateName).toBe('NSW');
    expect(component.company.primaryAddress.zipCode).toBe('2000');
  });

  it('should handle loading state during data fetch', () => {
    expect(component.isLoading).toBeFalse();

    component.ngOnInit();

    // Loading should be set to true during fetch, then false after
    expect(component.isLoading).toBeFalse();
  });
});
