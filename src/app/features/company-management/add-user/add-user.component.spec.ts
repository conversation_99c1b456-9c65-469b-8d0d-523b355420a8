import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { AddUserComponent } from './add-user.component';
import { NotificationService } from '../../../core/services/notification.service';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { UserCreationService } from '../../../core/services/user-creation.service';

describe('AddUserComponent', () => {
  let component: AddUserComponent;
  let fixture: ComponentFixture<AddUserComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockSharedLookupService: jasmine.SpyObj<SharedLookupService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockUserCreationService: jasmine.SpyObj<UserCreationService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockSharedLookupService = jasmine.createSpyObj('SharedLookupService', ['fetchRoles']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', ['fileSelected$', 'fileUploadEvent']);
    mockUserCreationService = jasmine.createSpyObj('UserCreationService', ['createUserWithProfilePicture', 'handleFileSelection']);

    mockCompanyStateService.fileSelected$ = of({ id: 1, fileData: null });

    await TestBed.configureTestingModule({
      imports: [
        AddUserComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: SharedLookupService, useValue: mockSharedLookupService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: UserCreationService, useValue: mockUserCreationService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddUserComponent);
    component = fixture.componentInstance;
    component.companyId = 1;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.addUserForm).toBeDefined();
    expect(component.addUserForm.get('firstName')?.value).toBe('');
    expect(component.addUserForm.get('lastName')?.value).toBe('');
    expect(component.addUserForm.get('email')?.value).toBe('');
    expect(component.addUserForm.get('contactNumber')?.value).toBe('');
    expect(component.addUserForm.get('roleId')?.value).toBeNull();
  });

  it('should validate required fields', () => {
    const form = component.addUserForm;
    expect(form.valid).toBeFalse();

    form.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    expect(form.valid).toBeTrue();
  });

  it('should validate email format', () => {
    const emailControl = component.addUserForm.get('email');

    emailControl?.setValue('invalid-email');
    expect(emailControl?.hasError('email')).toBeTrue();

    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.hasError('email')).toBeFalse();
  });

  it('should fetch roles on init', () => {
    mockSharedLookupService.fetchRoles.and.returnValue(of([]));

    component.ngOnInit();

    expect(mockSharedLookupService.fetchRoles).toHaveBeenCalled();
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
    const mockEvent = { target: { files: [mockFile] } } as any;

    component.onFileSelected(mockEvent);

    expect(mockUserCreationService.handleFileSelection).toHaveBeenCalled();
  });

  it('should check if email is unique', () => {
    component.existingUsers = [
      { email: '<EMAIL>' } as any
    ];

    expect(component.isEmailUnique('<EMAIL>')).toBeFalse();
    expect(component.isEmailUnique('<EMAIL>')).toBeTrue();
  });

  it('should validate user form', () => {
    component.addUserForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    expect(component.isUserFormValid()).toBeTrue();
  });

  it('should validate user form correctly', () => {
    component.addUserForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    expect(component.isUserFormValid()).toBeTrue();
  });

  it('should emit drawer closed event', () => {
    spyOn(component.drawerClosed, 'emit');

    component.toggleAddUserDrawer();

    expect(component.drawerClosed.emit).toHaveBeenCalled();
  });

  it('should add user when form is valid', () => {
    component.addUserForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    mockUserCreationService.createUserWithProfilePicture.and.returnValue(of({ success: true, data: {} }));

    component.addUser();

    expect(mockUserCreationService.createUserWithProfilePicture).toHaveBeenCalled();
  });
});
