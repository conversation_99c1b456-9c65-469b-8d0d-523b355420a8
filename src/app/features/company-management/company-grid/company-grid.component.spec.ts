import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyGridComponent } from './company-grid.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { NzModalService } from 'ng-zorro-antd/modal';

describe('CompanyGridComponent', () => {
  let component: CompanyGridComponent;
  let fixture: ComponentFixture<CompanyGridComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockModalService: jasmine.SpyObj<NzModalService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['getAllCompanies', 'updateCompanyStatus']);
    mockModalService = jasmine.createSpyObj('NzModalService', ['confirm']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: { params: {} },
      params: of({}),
      queryParams: of({})
    };

    await TestBed.configureTestingModule({
      imports: [
        CompanyGridComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: NzModalService, useValue: mockModalService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyGridComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.companyTableData).toEqual([]);
    expect(component.pageIndex).toBe(1);
    expect(component.pageSize).toBe(10);
    expect(component.total).toBe(0);
  });

  it('should fetch companies on init', () => {
    mockRegisterService.getAllCompanies.and.returnValue(of({
      success: true,
      data: { content: [], totalElements: 0 }
    }));

    component.ngOnInit();

    expect(mockRegisterService.getAllCompanies).toHaveBeenCalled();
  });

  it('should handle page change', () => {
    spyOn(component, 'getCompanies');

    component.onPageIndexChange(2);

    expect(component.pageIndex).toBe(2);
    expect(component.getCompanies).toHaveBeenCalled();
  });

  it('should handle page size change', () => {
    spyOn(component, 'getCompanies');

    component.onPageSizeChange(20);

    expect(component.pageSize).toBe(20);
    expect(component.pageIndex).toBe(1);
    expect(component.getCompanies).toHaveBeenCalled();
  });

  it('should navigate to view company', () => {
    const companyId = 1;

    component.viewCompany(companyId);

    expect(mockRouter.navigate).toHaveBeenCalledWith([companyId], { relativeTo: mockActivatedRoute });
  });

  it('should navigate to edit company', () => {
    const companyId = 1;

    component.editCompany(companyId);

    expect(mockRouter.navigate).toHaveBeenCalledWith(['edit', companyId], { relativeTo: mockActivatedRoute });
  });

  it('should show confirmation modal for status toggle', () => {
    const mockCompany = { id: 1, name: 'Test Company', active: true } as any;

    component.toggleCompanyStatus(mockCompany);

    expect(mockModalService.confirm).toHaveBeenCalled();
  });

  it('should update company status', () => {
    const mockCompany = { id: 1, name: 'Test Company', active: true } as any;
    mockRegisterService.updateCompanyStatus.and.returnValue(of({ success: true }));

    component.updateCompanyStatus(mockCompany);

    expect(mockRegisterService.updateCompanyStatus).toHaveBeenCalledWith(1, false);
  });

  it('should filter companies by search term', () => {
    component.companyTableData = [
      { id: 1, name: 'ABC Company', active: true },
      { id: 2, name: 'XYZ Corporation', active: true }
    ] as any[];

    component.onSearch('ABC');

    expect(component.filteredCompanyTableData.length).toBe(1);
    expect(component.filteredCompanyTableData[0].name).toBe('ABC Company');
  });

  it('should reset search when search term is empty', () => {
    component.companyTableData = [
      { id: 1, name: 'ABC Company', active: true },
      { id: 2, name: 'XYZ Corporation', active: true }
    ] as any[];

    component.onSearch('');

    expect(component.filteredCompanyTableData.length).toBe(2);
  });
});
