import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  FormsModule,
} from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';

import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

import { Subscription } from 'rxjs';

import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
  ApiResponseCompanyDTO,
  CompanyRequestDTO,
  AddressResponseDTO,
} from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';

@Component({
  selector: 'app-edit-company-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company-form.component.html',
  styleUrls: [
    './edit-company-form.component.scss',
    '../view-company/view-company.component.scss',
  ],
})
export class EditCompanyFormComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() company: ICompanyFields | null = null;
  @Output() companySaved = new EventEmitter<CompanyDTO>();
  @Output() formCancelled = new EventEmitter<void>();

  companyForm: FormGroup;
  isLoading = false;
  isBillingDetailsAccordionOpen = false;
  isSameAsCompanyDetails = false;

  states: StateDTO[] = [];
  postalCode: ZipCodeDTO[] = [];
  billingZipcode: ZipCodeDTO[] = [];

  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyStateService: CompanyStateService,
    private sharedLookupService: SharedLookupService,
    private cdr: ChangeDetectorRef,
  ) {
    this.companyForm = this.createCompanyForm();
  }

  private createCompanyForm(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      billingAddressLine1: ['', Validators.required],
      billingAddressLine2: [''],
      billingSuburb: [''],
      billingState: ['', Validators.required],
      billingPostalCode: ['', Validators.required],
    });
  }

  async ngOnInit(): Promise<void> {
    if (this.company) {
      await this.initializeForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isSaveButtonEnabled(): boolean {
    const isEnabled =
      this.companyForm.valid && this.companyForm.dirty && !this.isLoading;
    return isEnabled;
  }

  async initializeForm(): Promise<void> {
    if (!this.company) return;

    await this.fetchStates();
    this.populateFormWithCompanyData();
    await this.loadZipcodesForStates();
    this.setupFormSubscriptions();
    this.finalizeFormSetup();
  }

  private async loadZipcodesForStates(): Promise<void> {
    const primaryStateId = this.companyForm.get('state')?.value;
    const billingStateId = this.companyForm.get('billingState')?.value;

    const promises = [];
    if (primaryStateId) {
      promises.push(this.fetchZipcodes(primaryStateId, 'primary'));
    }
    if (billingStateId && !this.isSameAsCompanyDetails) {
      promises.push(this.fetchZipcodes(billingStateId, 'billing'));
    }

    await Promise.all(promises);
  }

  private finalizeFormSetup(): void {
    this.companyForm.markAsPristine();
    this.cdr.detectChanges();
  }

  private populateFormWithCompanyData(): void {
    if (!this.company) return;

    const { primaryStateId, billingStateId } = this.getStateIds();
    const formData = this.buildFormData(primaryStateId, billingStateId);

    this.isSameAsCompanyDetails = false;
    this.companyForm.patchValue(formData);
    this.enableBillingFields();

    setTimeout(() => this.companyForm.markAsPristine(), 100);
    this.cdr.detectChanges();
  }

  private getStateIds(): { primaryStateId: number | null; billingStateId: number | null } {
    const primaryState = this.states.find(s => s.stateName === this.company?.primaryAddress?.stateName);
    const billingState = this.states.find(s => s.stateName === this.company?.billingAddress?.stateName);

    return {
      primaryStateId: primaryState?.id || null,
      billingStateId: billingState?.id || null
    };
  }

  private buildFormData(primaryStateId: number | null, billingStateId: number | null): any {
    const { company } = this;
    return {
      name: company?.name || '',
      abn: company?.abn || '',
      acn: company?.acn || '',
      billingEmail: company?.billingEmail || '',
      accountsContactName: company?.accountsContactName || '',
      accountsContactNumber: company?.accountsContactNumber || '',
      addressLine1: company?.primaryAddress?.addressLine1 || '',
      addressLine2: company?.primaryAddress?.addressLine2 || '',
      suburb: company?.primaryAddress?.suburb || '',
      state: primaryStateId,
      postalCode: company?.primaryAddress?.zipCodeId || '',
      billingAddressLine1: company?.billingAddress?.addressLine1 || '',
      billingAddressLine2: company?.billingAddress?.addressLine2 || '',
      billingSuburb: company?.billingAddress?.suburb || '',
      billingState: billingStateId,
      billingPostalCode: company?.billingAddress?.zipCodeId || '',
    };
  }

  private setupFormSubscriptions(): void {
    this.setupStateChangeSubscription();
    this.setupBillingStateChangeSubscription();
    this.setupFormValueChangesSubscription();
  }

  private setupStateChangeSubscription(): void {
    this.subscriptions.add(
      this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        if (stateId) {
          this.handleStateChange(stateId, 'primary');
        }
      })
    );
  }

  private setupBillingStateChangeSubscription(): void {
    this.subscriptions.add(
      this.companyForm.get('billingState')?.valueChanges.subscribe((stateId) => {
        if (stateId && !this.isSameAsCompanyDetails) {
          this.handleStateChange(stateId, 'billing');
        }
      })
    );
  }

  private setupFormValueChangesSubscription(): void {
    this.subscriptions.add(
      this.companyForm.valueChanges.subscribe(() => this.cdr.detectChanges())
    );
  }

  private async handleStateChange(stateId: number, addressType: 'primary' | 'billing'): Promise<void> {
    await this.fetchZipcodes(stateId, addressType);
    this.resetPostalCodeForAddressType(addressType);
    if (addressType === 'primary' && this.isSameAsCompanyDetails) {
      this.syncBillingZipcodes();
    }
    this.cdr.detectChanges();
  }

  private resetPostalCodeForAddressType(addressType: 'primary' | 'billing'): void {
    const controlName = addressType === 'primary' ? 'postalCode' : 'billingPostalCode';
    this.companyForm.get(controlName)?.setValue('', { emitEvent: false });
  }

  private syncBillingZipcodes(): void {
    this.companyForm.get('billingPostalCode')?.setValue('', { emitEvent: false });
    this.billingZipcode = this.postalCode;
  }

  async fetchStates(): Promise<void> {
    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.sharedLookupService.fetchStates().subscribe({
          next: (states: StateDTO[]) => {
            this.isLoading = false;
            this.states = states;
            resolve();
          },
          error: (error: unknown) => {
            this.isLoading = false;
            console.error('Failed to fetch states:', error);
            reject();
          },
        }),
      );
    });
  }

  async fetchZipcodes(stateId: number, addressType: 'primary' | 'billing'): Promise<void> {
    if (!stateId) return;

    this.isLoading = true;
    return new Promise((resolve, reject) => {
      this.subscriptions.add(
        this.sharedLookupService.fetchZipcodes(stateId).subscribe({
          next: (zipcodes: ZipCodeDTO[]) => {
            this.isLoading = false;
            this.processZipcodes(zipcodes, addressType);
            this.handleZipcodesResult(zipcodes);
            resolve();
          },
          error: (error: unknown) => {
            this.isLoading = false;
            console.error(`Failed to fetch ${addressType} zipcodes:`, error);
            reject();
          },
        }),
      );
    });
  }

  private processZipcodes(zipcodes: ZipCodeDTO[], addressType: 'primary' | 'billing'): void {
    if (addressType === 'primary') {
      this.postalCode = zipcodes;
      this.setMatchingZipcode('postalCode', this.company?.primaryAddress?.zipCodeId, zipcodes);
    } else {
      this.billingZipcode = zipcodes;
      this.setMatchingZipcode('billingPostalCode', this.company?.billingAddress?.zipCodeId, zipcodes);
    }
  }

  private setMatchingZipcode(controlName: string, zipCodeId: number | undefined, zipcodes: ZipCodeDTO[]): void {
    if (zipCodeId) {
      const matchingZipcode = zipcodes.find(z => z.id === zipCodeId);
      if (matchingZipcode) {
        this.companyForm.get(controlName)?.setValue(matchingZipcode.id, { emitEvent: false });
      }
    }
  }

  private handleZipcodesResult(zipcodes: ZipCodeDTO[]): void {
    if (zipcodes.length === 0) {
      this.notification.warning(COMMON_STRINGS.errorMessages.failedToFetchZipcodes);
    }
    this.cdr.detectChanges();
  }

  saveCompanyDetails(): void {
    if (!this.companyForm.valid) {
      this.handleInvalidForm();
      return;
    }

    if (!this.company?.id) {
      this.notification.error(COMMON_STRINGS.warningMessages.companyIdNotAvailable);
      return;
    }

    const tempCompanyRequest = this.buildCompanyRequest();
    const tempCompany = this.buildTempCompany(tempCompanyRequest);
    this.updateCompanyDetails(tempCompanyRequest, tempCompany);
  }

  cancelEdit(): void {
    this.resetFormState();
    this.formCancelled.emit();
  }

  private resetFormState(): void {
    this.companyStateService.clearTempCompanyData();
    this.companyForm.reset();
    this.isSameAsCompanyDetails = false;
    this.isBillingDetailsAccordionOpen = false;
  }

  private handleInvalidForm(): void {
    this.companyForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private buildCompanyRequest(): CompanyRequestDTO {
    const formValue = this.companyForm.getRawValue();
    const { primaryAddress, billingAddress } = this.buildAddresses(formValue);

    return {
      name: formValue.name,
      abn: formValue.abn,
      acn: formValue.acn,
      billingEmail: formValue.billingEmail,
      accountsContactName: formValue.accountsContactName || undefined,
      accountsContactNumber: formValue.accountsContactNumber || undefined,
      industry: this.company?.industry,
      description: this.company?.description,
      website: this.company?.website,
      employeeCount: this.company?.employeeCount,
      isBillingPrimary: this.isSameAsCompanyDetails,
      primaryAddress,
      billingAddress,
    };
  }

  private buildAddresses(formValue: any): { primaryAddress: any; billingAddress: any } {
    const primaryAddress = {
      addressLine1: formValue.addressLine1,
      addressLine2: formValue.addressLine2 || undefined,
      suburb: formValue.suburb || undefined,
      stateId: Number(formValue.state),
      zipCodeId: Number(formValue.postalCode),
    };

    const billingAddress = this.isSameAsCompanyDetails ? primaryAddress : {
      addressLine1: formValue.billingAddressLine1,
      addressLine2: formValue.billingAddressLine2 || undefined,
      suburb: formValue.billingSuburb || undefined,
      stateId: Number(formValue.billingState),
      zipCodeId: Number(formValue.billingPostalCode),
    };

    return { primaryAddress, billingAddress };
  }

  private buildTempCompany(tempCompanyRequest: CompanyRequestDTO): ICompanyFields {
    return {
      ...this.companyStateService.getTempCompanyData(),
      id: this.company?.id,
      ...this.extractBasicCompanyFields(tempCompanyRequest),
      primaryAddress: this.buildAddressWithDetails(tempCompanyRequest.primaryAddress, AddressResponseDTO.AddressTypeEnum.Primary, this.postalCode),
      billingAddress: this.buildAddressWithDetails(tempCompanyRequest.billingAddress, AddressResponseDTO.AddressTypeEnum.Billing, this.billingZipcode),
    };
  }

  private extractBasicCompanyFields(request: CompanyRequestDTO): Partial<ICompanyFields> {
    return {
      name: request.name,
      abn: request.abn,
      acn: request.acn,
      billingEmail: request.billingEmail,
      accountsContactName: request.accountsContactName,
      accountsContactNumber: request.accountsContactNumber,
      industry: request.industry,
      description: request.description,
      website: request.website,
      employeeCount: request.employeeCount,
    };
  }

  private buildAddressWithDetails(address: any, addressType: AddressResponseDTO.AddressTypeEnum, zipcodes: ZipCodeDTO[]): any {
    return {
      ...address,
      addressType,
      stateName: this.states.find(s => s.id === address?.stateId)?.stateName,
      zipCode: zipcodes.find(z => z.id === address?.zipCodeId)?.zipCode,
    };
  }

  private updateCompanyDetails(tempCompanyRequest: CompanyRequestDTO, tempCompany: ICompanyFields): void {
    this.isLoading = true;
    this.subscriptions.add(
      this.registerService.updateCompany(this.company!.id!, tempCompanyRequest).subscribe({
        next: (response: ApiResponseCompanyDTO) => this.handleUpdateSuccess(response, tempCompany),
        error: (error) => this.handleUpdateError(error),
      }),
    );
  }

  private handleUpdateSuccess(response: ApiResponseCompanyDTO, tempCompany: ICompanyFields): void {
    this.isLoading = false;
    if (response.success && response.data) {
      this.processSuccessfulUpdate(response, tempCompany);
    } else {
      this.handleUpdateFailure(response.message);
    }
  }

  private processSuccessfulUpdate(response: ApiResponseCompanyDTO, tempCompany: ICompanyFields): void {
    this.company = { ...tempCompany, ...response.data };
    this.companyStateService.setCompanyData(this.company);
    this.companyStateService.clearTempCompanyData();
    this.resetUpdateState();
    this.notification.success(COMMON_STRINGS.successMessages.companyUpdateSuccess);
    this.companySaved.emit(response.data!);
  }

  private resetUpdateState(): void {
    this.isBillingDetailsAccordionOpen = false;
    this.isSameAsCompanyDetails = false;
  }

  private handleUpdateFailure(message?: string): void {
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace('${errorMessage}', message || '')
    );
  }

  private handleUpdateError(error: HttpErrorResponse): void {
    this.isLoading = false;
    const errorMessage = error.error?.message || error.message;
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace('${errorMessage}', errorMessage)
    );
  }

  onSameAsCompanyDetailsChange(checked: boolean): void {
    this.isSameAsCompanyDetails = checked;

    if (checked) {
      this.copyPrimaryToBillingAddress();
      this.disableBillingFields();
    } else {
      this.enableBillingFields();
    }

    this.companyForm.markAsDirty();
    this.cdr.detectChanges();
  }

  private copyPrimaryToBillingAddress(): void {
    const primaryValues = {
      billingAddressLine1: this.companyForm.get('addressLine1')?.value || '',
      billingAddressLine2: this.companyForm.get('addressLine2')?.value || '',
      billingSuburb: this.companyForm.get('suburb')?.value || '',
      billingState: this.companyForm.get('state')?.value || '',
      billingPostalCode: this.companyForm.get('postalCode')?.value || '',
    };
    this.companyForm.patchValue(primaryValues);
    this.billingZipcode = this.postalCode;
  }

  private readonly billingFields = [
    'billingAddressLine1', 'billingAddressLine2', 'billingSuburb', 'billingState', 'billingPostalCode'
  ];

  private disableBillingFields(): void {
    this.billingFields.forEach(field => this.companyForm.get(field)?.disable());
  }

  private enableBillingFields(): void {
    this.billingFields.forEach(field => this.companyForm.get(field)?.enable());
  }

  getCompanyErrorTip(controlName: string): string | undefined {
    const control = this.companyForm.get(controlName);
    if (!control?.touched) return undefined;

    if (control.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }

    const errorMap: Record<string, string> = {
      billingEmail: COMMON_STRINGS.warningMessages.enterValidEmail,
      accountsContactNumber: COMMON_STRINGS.warningMessages.enterValidPhoneNumber,
    };

    if (controlName in errorMap && control.hasError(controlName === 'billingEmail' ? 'email' : 'pattern')) {
      return errorMap[controlName];
    }

    return undefined;
  }

  isCompanyDataUnchanged(original: ICompanyFields, updated: ICompanyFields): boolean {
    return this.compareBasicFields(original, updated) && this.compareAddressFields(original, updated);
  }

  private compareBasicFields(original: ICompanyFields, updated: ICompanyFields): boolean {
    const fieldsToCompare: (keyof ICompanyFields)[] = [
      'name', 'abn', 'acn', 'billingEmail', 'accountsContactName', 'accountsContactNumber'
    ];
    return fieldsToCompare.every(field => original[field] === updated[field]);
  }

  private compareAddressFields(original: ICompanyFields, updated: ICompanyFields): boolean {
    const addressFields: (keyof AddressResponseDTO)[] = [
      'addressLine1', 'addressLine2', 'suburb', 'stateId', 'zipCodeId'
    ];

    return addressFields.every(field =>
      (original.primaryAddress?.[field] ?? '') === (updated.primaryAddress?.[field] ?? '') &&
      (original.billingAddress?.[field] ?? '') === (updated.billingAddress?.[field] ?? '')
    );
  }

  toggleBillingDetailsAccordion(): void {
    this.isBillingDetailsAccordionOpen = !this.isBillingDetailsAccordionOpen;
  }
}
