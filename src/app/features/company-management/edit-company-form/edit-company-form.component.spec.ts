import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { EditCompanyFormComponent } from './edit-company-form.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { CompanyFormService } from '../../../core/services/company-form.service';
import { CompanyValidationService } from '../../../core/services/company-validation.service';

describe('EditCompanyFormComponent', () => {
  let component: EditCompanyFormComponent;
  let fixture: ComponentFixture<EditCompanyFormComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockCompanyFormService: jasmine.SpyObj<CompanyFormService>;
  let mockCompanyValidationService: jasmine.SpyObj<CompanyValidationService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['updateCompany']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getTempCompanyData', 'setCompanyData', 'clearTempCompanyData'
    ]);
    mockCompanyFormService = jasmine.createSpyObj('CompanyFormService', [
      'createEditCompanyForm', 'populateFormWithCompanyData', 'buildCompanyRequest',
      'buildTempCompany', 'copyPrimaryToBillingAddress', 'manageBillingFields',
      'fetchStates', 'fetchZipcodes'
    ]);
    mockCompanyValidationService = jasmine.createSpyObj('CompanyValidationService', [
      'isSaveButtonEnabled', 'getCompanyErrorTip', 'isCompanyDataUnchanged', 'handleInvalidForm'
    ]);

    // Mock form creation
    const mockForm = jasmine.createSpyObj('FormGroup', ['patchValue', 'get', 'valueChanges', 'markAsPristine', 'getRawValue', 'markAllAsTouched']);
    const mockControl = jasmine.createSpyObj('FormControl', ['setValue', 'valueChanges']);
    mockControl.value = 'test value';
    mockControl.valueChanges = of('test value');
    mockForm.get.and.returnValue(mockControl);
    mockForm.valueChanges = of({});
    mockForm.valid = true;
    mockForm.dirty = false;
    mockForm.getRawValue.and.returnValue({});

    mockCompanyFormService.createEditCompanyForm.and.returnValue(mockForm);
    mockCompanyFormService.fetchStates.and.returnValue(of([]));
    mockCompanyFormService.fetchZipcodes.and.returnValue(of([]));
    mockCompanyValidationService.isSaveButtonEnabled.and.returnValue(false);

    await TestBed.configureTestingModule({
      imports: [
        EditCompanyFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: CompanyFormService, useValue: mockCompanyFormService },
        { provide: CompanyValidationService, useValue: mockCompanyValidationService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(EditCompanyFormComponent);
    component = fixture.componentInstance;

    // Set up mock company data
    component.company = {
      id: 1,
      name: 'Test Company',
      abn: '*********',
      acn: '*********',
      primaryAddress: {
        addressLine1: '123 Test St',
        stateName: 'NSW',
        zipCodeId: 1
      },
      billingAddress: {
        addressLine1: '456 Billing St',
        stateName: 'VIC',
        zipCodeId: 2
      }
    } as any;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form using CompanyFormService', () => {
    expect(mockCompanyFormService.createEditCompanyForm).toHaveBeenCalled();
  });

  it('should check save button enabled state', () => {
    component.isSaveButtonEnabled;
    expect(mockCompanyValidationService.isSaveButtonEnabled).toHaveBeenCalled();
  });

  it('should initialize form when company is provided', async () => {
    mockCompanyFormService.fetchStates.and.returnValue(of([]));

    await component.initializeForm();

    expect(mockCompanyFormService.fetchStates).toHaveBeenCalled();
    expect(mockCompanyFormService.populateFormWithCompanyData).toHaveBeenCalled();
  });

  it('should not initialize form when company is not provided', async () => {
    component.company = null;

    await component.initializeForm();

    expect(mockCompanyFormService.fetchStates).not.toHaveBeenCalled();
  });

  it('should save company details when form is valid', () => {
    mockRegisterService.updateCompany.and.returnValue(of({ success: true, data: {} }));
    mockCompanyFormService.buildCompanyRequest.and.returnValue({} as any);
    mockCompanyFormService.buildTempCompany.and.returnValue({} as any);

    // Mock form as valid
    Object.defineProperty(component.companyForm, 'valid', { value: true, writable: false });

    component.saveCompanyDetails();

    expect(mockRegisterService.updateCompany).toHaveBeenCalled();
  });

  it('should handle invalid form', () => {
    // Mock form as invalid
    Object.defineProperty(component.companyForm, 'valid', { value: false, writable: false });

    component.saveCompanyDetails();

    expect(mockCompanyValidationService.handleInvalidForm).toHaveBeenCalled();
  });

  it('should cancel edit and emit event', () => {
    spyOn(component.formCancelled, 'emit');

    component.cancelEdit();

    expect(mockCompanyStateService.clearTempCompanyData).toHaveBeenCalled();
    expect(component.formCancelled.emit).toHaveBeenCalled();
  });

  it('should handle same as company details change', () => {
    component.onSameAsCompanyDetailsChange(true);

    expect(component.isSameAsCompanyDetails).toBeTrue();
    expect(mockCompanyFormService.manageBillingFields).toHaveBeenCalledWith(component.companyForm, true);
  });

  it('should get error tip for form controls', () => {
    const controlName = 'name';

    component.getCompanyErrorTip(controlName);

    expect(mockCompanyValidationService.getCompanyErrorTip).toHaveBeenCalledWith(component.companyForm, controlName);
  });

  it('should check if company data is unchanged', () => {
    const original = {} as any;
    const updated = {} as any;

    component.isCompanyDataUnchanged(original, updated);

    expect(mockCompanyValidationService.isCompanyDataUnchanged).toHaveBeenCalledWith(original, updated);
  });

  it('should toggle billing details accordion', () => {
    const initialState = component.isBillingDetailsAccordionOpen;

    component.toggleBillingDetailsAccordion();

    expect(component.isBillingDetailsAccordionOpen).toBe(!initialState);
  });

  it('should fetch zipcodes for given state', async () => {
    const stateId = 1;
    const addressType = 'primary';

    await component.fetchZipcodes(stateId, addressType);

    expect(mockCompanyFormService.fetchZipcodes).toHaveBeenCalledWith(stateId);
  });

  it('should handle successful company update', () => {
    spyOn(component.companySaved, 'emit');
    const mockResponse = { success: true, data: { id: 1 } };
    mockRegisterService.updateCompany.and.returnValue(of(mockResponse));
    mockCompanyFormService.buildCompanyRequest.and.returnValue({} as any);
    mockCompanyFormService.buildTempCompany.and.returnValue({} as any);

    component.saveCompanyDetails();

    expect(component.companySaved.emit).toHaveBeenCalled();
    expect(mockNotificationService.success).toHaveBeenCalled();
  });
});
