import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyUserOnboardFormComponent } from './company-user-onboard-form.component';
import { NotificationService } from '../../../core/services/notification.service';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { CompanyStateService } from '../../../core/services/company-state.service';
import { UserCreationService } from '../../../core/services/user-creation.service';

describe('CompanyUserOnboardFormComponent', () => {
  let component: CompanyUserOnboardFormComponent;
  let fixture: ComponentFixture<CompanyUserOnboardFormComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockSharedLookupService: jasmine.SpyObj<SharedLookupService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockUserCreationService: jasmine.SpyObj<UserCreationService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockSharedLookupService = jasmine.createSpyObj('SharedLookupService', ['fetchRoles']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState', 'updateUserDetails', 'fileSelected$', 'triggerFileInput', 'fileUploadEvent'
    ]);
    mockUserCreationService = jasmine.createSpyObj('UserCreationService', [
      'createUserWithProfilePicture', 'handleFileSelection'
    ]);

    mockSharedLookupService.fetchRoles.and.returnValue(of([]));
    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      userDetails: null
    });
    mockCompanyStateService.fileSelected$ = of({ id: 1, fileData: null });

    await TestBed.configureTestingModule({
      imports: [
        CompanyUserOnboardFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: SharedLookupService, useValue: mockSharedLookupService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: UserCreationService, useValue: mockUserCreationService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyUserOnboardFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.userForm).toBeDefined();
    expect(component.userForm.get('firstName')?.value).toBe('');
    expect(component.userForm.get('lastName')?.value).toBe('');
    expect(component.userForm.get('email')?.value).toBe('');
    expect(component.userForm.get('contactNumber')?.value).toBe('');
    expect(component.userForm.get('roleId')?.value).toBeNull();
  });

  it('should fetch roles on init', () => {
    component.ngOnInit();
    expect(mockSharedLookupService.fetchRoles).toHaveBeenCalled();
  });

  it('should validate required fields', () => {
    const form = component.userForm;
    expect(form.valid).toBeFalse();

    form.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    expect(form.valid).toBeTrue();
  });

  it('should validate email format', () => {
    const emailControl = component.userForm.get('email');

    emailControl?.setValue('invalid-email');
    expect(emailControl?.hasError('email')).toBeTrue();

    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.hasError('email')).toBeFalse();
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
    const mockEvent = { target: { files: [mockFile] } } as any;

    component.onFileSelected(mockEvent);

    expect(mockUserCreationService.handleFileSelection).toHaveBeenCalled();
  });

  it('should trigger file input', () => {
    const index = 0;

    component.triggerFileInput(index);

    expect(mockCompanyStateService.triggerFileInput).toHaveBeenCalledWith(index);
  });

  it('should check if user form is valid', () => {
    component.userForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    expect(component.isUserFormValid()).toBeTrue();
  });

  it('should check if email is unique', () => {
    component.existingUsers = [
      { email: '<EMAIL>' } as any
    ];

    expect(component.isEmailUnique('<EMAIL>')).toBeFalse();
    expect(component.isEmailUnique('<EMAIL>')).toBeTrue();
  });

  it('should add user when form is valid', () => {
    component.userForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    mockUserCreationService.createUserWithProfilePicture.and.returnValue(of({ success: true, data: {} }));

    component.addUser();

    expect(mockUserCreationService.createUserWithProfilePicture).toHaveBeenCalled();
  });

  it('should emit user added event', () => {
    spyOn(component.userAdded, 'emit');
    const mockUser = { id: 1, firstName: 'John' } as any;

    // Simulate successful user creation
    component.userForm.patchValue({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      contactNumber: '1234567890',
      roleId: 1
    });

    mockUserCreationService.createUserWithProfilePicture.and.returnValue(of({ success: true, data: mockUser }));

    component.addUser();

    expect(component.userAdded.emit).toHaveBeenCalled();
  });

  it('should get error tip for form controls', () => {
    const firstNameControl = component.userForm.get('firstName');
    firstNameControl?.markAsTouched();
    firstNameControl?.setValue('');

    const errorTip = component.getErrorTip('firstName');
    expect(errorTip).toContain('FirstName is required');
  });

  it('should handle loading state', () => {
    expect(component.isLoading).toBeFalse();

    component.isLoading = true;
    expect(component.isLoading).toBeTrue();
  });
});
