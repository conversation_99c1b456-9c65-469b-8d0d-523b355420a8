import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyComponent } from './company.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyStateService } from '../../../core/services/company-state.service';

describe('CompanyComponent', () => {
  let component: CompanyComponent;
  let fixture: ComponentFixture<CompanyComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['createCompany']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState', 'clearOnboardingState', 'setCompanyData'
    ]);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: { params: {} },
      params: of({}),
      queryParams: of({})
    };

    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null
    });

    await TestBed.configureTestingModule({
      imports: [
        CompanyComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyStateService, useValue: mockCompanyStateService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentStep).toBe(0);
    expect(component.isLoading).toBeFalse();
    expect(component.sameAsCompanyDetails).toBeFalse();
  });

  it('should handle step navigation', () => {
    component.currentStep = 0;

    component.nextStep();
    expect(component.currentStep).toBe(1);

    component.previousStep();
    expect(component.currentStep).toBe(0);
  });

  it('should not go to previous step when at first step', () => {
    component.currentStep = 0;

    component.previousStep();
    expect(component.currentStep).toBe(0);
  });

  it('should not go to next step when at last step', () => {
    component.currentStep = 2;

    component.nextStep();
    expect(component.currentStep).toBe(2);
  });

  it('should handle same as company details change', () => {
    const newValue = true;

    component.onSameAsCompanyDetailsChange(newValue);

    expect(component.sameAsCompanyDetails).toBe(newValue);
  });

  it('should handle user added event', () => {
    const mockUser = { id: 1, firstName: 'John', lastName: 'Doe' } as any;

    component.onUserAdded(mockUser);

    expect(component.addedUsers.length).toBe(1);
    expect(component.addedUsers[0]).toBe(mockUser);
  });

  it('should create company when form is submitted', () => {
    mockRegisterService.createCompany.and.returnValue(of({ success: true, data: { id: 1 } }));

    component.onSubmit();

    expect(mockRegisterService.createCompany).toHaveBeenCalled();
  });

  it('should navigate to company grid after successful creation', () => {
    mockRegisterService.createCompany.and.returnValue(of({ success: true, data: { id: 1 } }));

    component.onSubmit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/company-management']);
  });

  it('should handle company creation error', () => {
    mockRegisterService.createCompany.and.returnValue(of({ success: false, message: 'Error' }));

    component.onSubmit();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should check if current step is company step', () => {
    component.currentStep = 0;
    expect(component.isCompanyStep).toBeTrue();

    component.currentStep = 1;
    expect(component.isCompanyStep).toBeFalse();
  });

  it('should check if current step is billing step', () => {
    component.currentStep = 1;
    expect(component.isBillingStep).toBeTrue();

    component.currentStep = 0;
    expect(component.isBillingStep).toBeFalse();
  });

  it('should check if current step is user step', () => {
    component.currentStep = 2;
    expect(component.isUserStep).toBeTrue();

    component.currentStep = 0;
    expect(component.isUserStep).toBeFalse();
  });

  it('should check if current step is preview step', () => {
    component.currentStep = 3;
    expect(component.isPreviewStep).toBeTrue();

    component.currentStep = 0;
    expect(component.isPreviewStep).toBeFalse();
  });

  it('should handle loading state', () => {
    expect(component.isLoading).toBeFalse();

    component.isLoading = true;
    expect(component.isLoading).toBeTrue();
  });

  it('should clear onboarding state on destroy', () => {
    component.ngOnDestroy();

    expect(mockCompanyStateService.clearOnboardingState).toHaveBeenCalled();
  });
});
