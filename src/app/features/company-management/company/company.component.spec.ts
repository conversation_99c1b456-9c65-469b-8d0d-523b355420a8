import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { CompanyComponent } from './company.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyStateService } from '../../../core/services/company-state.service';

describe('CompanyComponent', () => {
  let component: CompanyComponent;
  let fixture: ComponentFixture<CompanyComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['createCompany']);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState', 'resetOnboardingState', 'setCompanyData'
    ]);

    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null,
      currentStep: 0
    });

    await TestBed.configureTestingModule({
      imports: [
        CompanyComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyStateService, useValue: mockCompanyStateService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.showOnboarding).toBeFalse();
    expect(component.activeTabIndex).toBe(0);
  });

  it('should handle tab index change', () => {
    component.onTabIndexChange(1);
    expect(component.activeTabIndex).toBe(1);
  });

  it('should show onboarding form', () => {
    component.showOnboardingForm();
    expect(component.showOnboarding).toBeTrue();
  });

  it('should cancel onboarding', () => {
    spyOn(component.companyClose, 'emit');
    component.cancelOnboarding();
    expect(component.showOnboarding).toBeFalse();
    expect(component.companyClose.emit).toHaveBeenCalledWith(null);
  });

  it('should check if first step', () => {
    expect(component.isFirstStep()).toBeDefined();
  });

  it('should check if last step', () => {
    expect(component.isLastStep()).toBeDefined();
  });

  it('should validate current step', () => {
    expect(component.isCurrentStepValid()).toBeDefined();
  });

  it('should handle loading state', () => {
    component.isLoading = true;
    expect(component.isLoading).toBeTrue();
  });

  it('should initialize onboarding state on init', () => {
    component.ngOnInit();
    expect(mockCompanyStateService.resetOnboardingState).toHaveBeenCalled();
  });
});
