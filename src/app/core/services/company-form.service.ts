import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  CompanyRequestDTO,
  StateDTO,
  ZipCodeDTO,
  AddressResponseDTO,
} from '../../api-client';
import { PHONE_REGEX } from '../constants/common';
import { ICompanyFields } from '../interface/company-fields';
import { SharedLookupService } from './shared-lookup.service';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root',
})
export class CompanyFormService {
  constructor(
    private fb: FormBuilder,
    private sharedLookupService: SharedLookupService,
    private notification: NotificationService,
  ) {}

  /**
   * Create company form with standard validation
   */
  createCompanyForm(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.required, Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
    });
  }

  /**
   * Create edit company form with billing fields
   */
  createEditCompanyForm(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', [Validators.email]],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: ['', Validators.required],
      billingAddressLine1: ['', Validators.required],
      billingAddressLine2: [''],
      billingSuburb: [''],
      billingState: ['', Validators.required],
      billingPostalCode: ['', Validators.required],
    });
  }

  /**
   * Create billing form with conditional validation
   */
  createBillingForm(): FormGroup {
    return this.fb.group({
      name: [''],
      abn: ['', Validators.required],
      acn: ['', Validators.required],
      billingEmail: ['', Validators.email],
      accountsContactName: [''],
      accountsContactNumber: ['', Validators.pattern(PHONE_REGEX)],
      addressLine1: [''],
      addressLine2: [''],
      suburb: [''],
      state: [''],
      postalCode: [''],
      sameAsCompanyDetails: [false],
    });
  }

  /**
   * Populate form with company data
   */
  populateFormWithCompanyData(
    form: FormGroup,
    company: ICompanyFields,
    states: StateDTO[],
  ): void {
    const { primaryStateId, billingStateId } = this.getStateIds(company, states);
    const formData = this.buildFormData(company, primaryStateId, billingStateId);
    form.patchValue(formData);
  }

  /**
   * Build company request from form data
   */
  buildCompanyRequest(
    formValue: any,
    company: ICompanyFields | null,
    isSameAsCompanyDetails: boolean,
  ): CompanyRequestDTO {
    const { primaryAddress, billingAddress } = this.buildAddresses(
      formValue,
      isSameAsCompanyDetails,
    );

    return {
      name: formValue.name,
      abn: formValue.abn,
      acn: formValue.acn,
      billingEmail: formValue.billingEmail,
      accountsContactName: formValue.accountsContactName || undefined,
      accountsContactNumber: formValue.accountsContactNumber || undefined,
      industry: company?.industry,
      description: company?.description,
      website: company?.website,
      employeeCount: company?.employeeCount,
      isBillingPrimary: isSameAsCompanyDetails,
      primaryAddress,
      billingAddress,
    };
  }

  /**
   * Build temp company object
   */
  buildTempCompany(
    tempCompanyRequest: CompanyRequestDTO,
    company: ICompanyFields | null,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
    tempCompanyData: any,
  ): ICompanyFields {
    return {
      ...tempCompanyData,
      id: company?.id,
      ...this.extractBasicCompanyFields(tempCompanyRequest),
      primaryAddress: this.buildAddressWithDetails(
        tempCompanyRequest.primaryAddress,
        AddressResponseDTO.AddressTypeEnum.Primary,
        states,
        postalCode,
      ),
      billingAddress: this.buildAddressWithDetails(
        tempCompanyRequest.billingAddress,
        AddressResponseDTO.AddressTypeEnum.Billing,
        states,
        billingZipcode,
      ),
    };
  }

  /**
   * Copy primary address to billing address
   */
  copyPrimaryToBillingAddress(form: FormGroup): any {
    return {
      billingAddressLine1: form.get('addressLine1')?.value || '',
      billingAddressLine2: form.get('addressLine2')?.value || '',
      billingSuburb: form.get('suburb')?.value || '',
      billingState: form.get('state')?.value || '',
      billingPostalCode: form.get('postalCode')?.value || '',
    };
  }

  /**
   * Manage billing field states
   */
  manageBillingFields(form: FormGroup, disable: boolean): void {
    const billingFields = [
      'billingAddressLine1',
      'billingAddressLine2',
      'billingSuburb',
      'billingState',
      'billingPostalCode',
    ];

    billingFields.forEach((field) => {
      const control = form.get(field);
      if (control) {
        disable ? control.disable() : control.enable();
      }
    });
  }

  /**
   * Get default billing details object
   */
  getDefaultBillingDetailsObject(): any {
    return {
      name: '',
      abn: '',
      acn: '',
      billingEmail: '',
      accountsContactName: '',
      accountsContactNumber: '',
      addressLine1: '',
      addressLine2: '',
      suburb: '',
      state: '',
      postalCode: '',
      sameAsCompanyDetails: true,
    };
  }

  /**
   * Clear billing form
   */
  clearBillingForm(form: FormGroup): void {
    const emptyFormData = {
      name: '',
      abn: '',
      acn: '',
      billingEmail: '',
      accountsContactName: '',
      accountsContactNumber: '',
      addressLine1: '',
      addressLine2: '',
      suburb: '',
      state: '',
      postalCode: '',
    };
    form.patchValue(emptyFormData);
  }

  /**
   * Fetch states observable
   */
  fetchStates(): Observable<StateDTO[]> {
    return this.sharedLookupService.fetchStates();
  }

  /**
   * Fetch zipcodes observable
   */
  fetchZipcodes(stateId: number): Observable<ZipCodeDTO[]> {
    return this.sharedLookupService.fetchZipcodes(stateId);
  }

  // Private helper methods
  private getStateIds(
    company: ICompanyFields,
    states: StateDTO[],
  ): { primaryStateId: number | null; billingStateId: number | null } {
    const primaryState = states.find(
      (s) => s.stateName === company?.primaryAddress?.stateName,
    );
    const billingState = states.find(
      (s) => s.stateName === company?.billingAddress?.stateName,
    );

    return {
      primaryStateId: primaryState?.id || null,
      billingStateId: billingState?.id || null,
    };
  }

  private buildFormData(
    company: ICompanyFields,
    primaryStateId: number | null,
    billingStateId: number | null,
  ): any {
    return {
      name: company?.name || '',
      abn: company?.abn || '',
      acn: company?.acn || '',
      billingEmail: company?.billingEmail || '',
      accountsContactName: company?.accountsContactName || '',
      accountsContactNumber: company?.accountsContactNumber || '',
      addressLine1: company?.primaryAddress?.addressLine1 || '',
      addressLine2: company?.primaryAddress?.addressLine2 || '',
      suburb: company?.primaryAddress?.suburb || '',
      state: primaryStateId,
      postalCode: company?.primaryAddress?.zipCodeId || '',
      billingAddressLine1: company?.billingAddress?.addressLine1 || '',
      billingAddressLine2: company?.billingAddress?.addressLine2 || '',
      billingSuburb: company?.billingAddress?.suburb || '',
      billingState: billingStateId,
      billingPostalCode: company?.billingAddress?.zipCodeId || '',
    };
  }

  private buildAddresses(
    formValue: any,
    isSameAsCompanyDetails: boolean,
  ): { primaryAddress: any; billingAddress: any } {
    const primaryAddress = {
      addressLine1: formValue.addressLine1,
      addressLine2: formValue.addressLine2 || undefined,
      suburb: formValue.suburb || undefined,
      stateId: Number(formValue.state),
      zipCodeId: Number(formValue.postalCode),
    };

    const billingAddress = isSameAsCompanyDetails
      ? primaryAddress
      : {
          addressLine1: formValue.billingAddressLine1,
          addressLine2: formValue.billingAddressLine2 || undefined,
          suburb: formValue.billingSuburb || undefined,
          stateId: Number(formValue.billingState),
          zipCodeId: Number(formValue.billingPostalCode),
        };

    return { primaryAddress, billingAddress };
  }

  private extractBasicCompanyFields(request: CompanyRequestDTO): Partial<ICompanyFields> {
    return {
      name: request.name,
      abn: request.abn,
      acn: request.acn,
      billingEmail: request.billingEmail,
      accountsContactName: request.accountsContactName,
      accountsContactNumber: request.accountsContactNumber,
      industry: request.industry,
      description: request.description,
      website: request.website,
      employeeCount: request.employeeCount,
    };
  }

  private buildAddressWithDetails(
    address: any,
    addressType: AddressResponseDTO.AddressTypeEnum,
    states: StateDTO[],
    zipcodes: ZipCodeDTO[],
  ): any {
    return {
      ...address,
      addressType,
      stateName: states.find((s) => s.id === address?.stateId)?.stateName,
      zipCode: zipcodes.find((z) => z.id === address?.zipCodeId)?.zipCode,
    };
  }
}
